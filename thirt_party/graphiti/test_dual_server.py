#!/usr/bin/env python3
"""
测试双服务器配置的脚本
"""

import os
import sys

# 添加路径以便导入
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mcp_server'))

def test_imports():
    """测试导入是否正常"""
    try:
        # 测试基本导入
        from graphiti_mcp_server2 import API_SERVER_PORT, MCP_SERVER_SSE_PORT
        print(f"✓ 端口配置导入成功:")
        print(f"  API_SERVER_PORT: {API_SERVER_PORT}")
        print(f"  MCP_SERVER_SSE_PORT: {MCP_SERVER_SSE_PORT}")
        
        # 测试FastMCP导入
        from graphiti_mcp_server2 import mcp
        print(f"✓ FastMCP实例导入成功")
        
        # 测试配置类导入
        from graphiti_mcp_server2 import GraphitiConfig, MCPConfig
        print(f"✓ 配置类导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_graph_service_import():
    """测试graph_service导入"""
    try:
        # 添加graph_service路径
        graph_service_path = os.path.join(os.path.dirname(__file__), 'server')
        if graph_service_path not in sys.path:
            sys.path.insert(0, graph_service_path)
        
        from graph_service.main import app as fastapi_app
        print(f"✓ graph_service.main导入成功")
        print(f"  FastAPI应用类型: {type(fastapi_app)}")
        
        return True
    except ImportError as e:
        print(f"✗ graph_service导入失败: {e}")
        print(f"  请确保graph_service目录存在于: {graph_service_path}")
        return False

def main():
    """主测试函数"""
    print("=== 双服务器配置测试 ===\n")
    
    # 测试基本导入
    print("1. 测试基本导入...")
    basic_ok = test_imports()
    
    print("\n2. 测试graph_service导入...")
    graph_ok = test_graph_service_import()
    
    print(f"\n=== 测试结果 ===")
    print(f"基本导入: {'✓ 通过' if basic_ok else '✗ 失败'}")
    print(f"graph_service导入: {'✓ 通过' if graph_ok else '✗ 失败'}")
    
    if basic_ok and graph_ok:
        print("\n🎉 所有测试通过！双服务器配置准备就绪。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
